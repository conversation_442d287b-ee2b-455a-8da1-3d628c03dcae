"use client"

import { useState, useEffect, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { UserPlus, Link2, Co<PERSON>, Check, Clock, Users, Mail, AlertTriangle } from "lucide-react"
import { useRealtimeSquadInvitations } from "@/lib/domains/invitation/invitation.realtime.hooks"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { Squad } from "@/lib/domains/squad/squad.types"
import { InvitationList } from "./invitation-list"
import { PageLoading } from "@/components/page-loading"
import { toast } from "@/components/ui/use-toast"
import { ErrorBoundary } from "@/components/error-boundary"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  useInvitationLink,
  useGenerateInvitationLink,
  useRealtimeSquadInvitationSends,
} from "@/lib/domains/invitation/invitation.hooks"
import { generateInvitationLink } from "@/lib/email-service"

interface InvitationsTabProps {
  squad: Squad
  onInviteClick?: () => void
}

export function InvitationsTab({ squad, onInviteClick }: InvitationsTabProps) {
  const squadId = squad.id
  const user = useUser()
  const [copiedLink, setCopiedLink] = useState(false)

  // Get legacy invitations
  const {
    invitations,
    loading: invitationsLoading,
    error: invitationsError,
  } = useRealtimeSquadInvitations(squadId)

  // Get invitation link
  const { invitationLink, loading: linkLoading } = useInvitationLink(squadId)
  const { generate: generateLink, generating } = useGenerateInvitationLink()

  const [isSquadLead, setIsSquadLead] = useState(false)

  // Check if current user is squad leader
  useEffect(() => {
    if (user) {
      setIsSquadLead(user.uid === squad.leaderId)
    } else {
      setIsSquadLead(false)
    }
  }, [user, squad.leaderId])

  // Get invitation sends (real-time) - only for squad leaders
  const { invitationSends } = useRealtimeSquadInvitationSends(isSquadLead ? squadId : "")

  // Show error toast if there's an error
  useEffect(() => {
    if (invitationsError) {
      toast({
        title: "Error",
        description: "Unable to load invitations. Please try again later.",
        variant: "destructive",
      })
    }
  }, [invitationsError])

  // Generate or get invitation link
  const handleGenerateLink = async () => {
    if (invitationLink) return invitationLink

    const newLink = await generateLink(squad.id, squad.name)
    if (newLink) {
      toast({
        title: "Invitation link generated",
        description: "Your shareable invitation link is ready!",
      })
    }
    return newLink
  }

  // Copy invitation link to clipboard
  const handleCopyLink = async () => {
    const link = invitationLink || (await handleGenerateLink())
    if (!link) return

    const fullLink = generateInvitationLink(link.id)

    try {
      await navigator.clipboard.writeText(fullLink)
      setCopiedLink(true)
      toast({
        title: "Link copied",
        description: "Invitation link copied to clipboard",
      })

      setTimeout(() => setCopiedLink(false), 2000)
    } catch (error) {
      toast({
        title: "Failed to copy",
        description: "Please copy the link manually",
        variant: "destructive",
      })
    }
  }

  // Format expiration date
  const formatExpirationDate = (expiresAt: any) => {
    if (!expiresAt) return ""
    const date = expiresAt.toDate ? expiresAt.toDate() : new Date(expiresAt)
    return date.toLocaleDateString()
  }

  // Get recent invitation sends for display
  const recentSends = useMemo(() => {
    if (!invitationLink || !invitationSends) return []

    return invitationSends
      .filter((send) => send.invitationId === invitationLink.id)
      .sort((a, b) => {
        const aDate = a.sentAt?.toDate ? a.sentAt.toDate() : new Date()
        const bDate = b.sentAt?.toDate ? b.sentAt.toDate() : new Date()
        return bDate.getTime() - aDate.getTime()
      })
      .slice(0, 10) // Show last 10 sends
  }, [invitationLink, invitationSends])

  if (invitationsLoading || linkLoading) {
    return <PageLoading />
  }

  if (!isSquadLead) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h3 className="font-medium mb-2">Access Restricted</h3>
          <p className="text-muted-foreground">
            Only the squad leader can view and manage invitations.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <ErrorBoundary>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Squad Invitations</h2>
          {onInviteClick && (
            <Button onClick={onInviteClick}>
              <UserPlus className="mr-2 h-4 w-4" /> Invite People
            </Button>
          )}
        </div>

        {/* Invitation Link Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Link2 className="h-5 w-5" />
              Shareable Invitation Link
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>
                {invitationLink
                  ? `Expires on ${formatExpirationDate(invitationLink.expiresAt)}`
                  : "Links expire after 10 days"}
              </span>
            </div>

            {invitationLink ? (
              <div className="space-y-3">
                <div className="flex items-center gap-2 p-3 bg-muted rounded-md">
                  <Input
                    value={generateInvitationLink(invitationLink.id)}
                    readOnly
                    className="flex-1 bg-background"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyLink}
                    disabled={linkLoading}
                  >
                    {copiedLink ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </div>

                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Users className="h-4 w-4" />
                  <span>Anyone with this link can join your squad</span>
                </div>
              </div>
            ) : (
              <div className="text-center py-6">
                <Button
                  onClick={handleGenerateLink}
                  disabled={generating || linkLoading}
                  className="flex items-center gap-2"
                >
                  <Link2 className="h-4 w-4" />
                  {generating ? "Generating..." : "Generate Invitation Link"}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Email Invitations */}
        {recentSends.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Recent Email Invitations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {recentSends.map((send) => (
                  <div
                    key={send.id}
                    className="flex items-center justify-between p-3 bg-muted rounded-md text-sm"
                  >
                    <span className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      {send.email}
                    </span>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {send.status}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {send.sentAt?.toDate ? send.sentAt.toDate().toLocaleDateString() : ""}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Legacy Individual Invitations */}
        {invitations && invitations.length > 0 && (
          <>
            <Separator />
            <div>
              <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-amber-500" />
                Legacy Invitations
              </h3>
              <InvitationList invitations={invitations} squadId={squadId} />
            </div>
          </>
        )}
      </div>
    </ErrorBoundary>
  )
}
