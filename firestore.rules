rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    function isSquadLeader(squadId) {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/squads/$(squadId)) &&
             get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid;
    }

    function isTripLeader(tripId) {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/trips/$(tripId)) &&
             get(/databases/$(database)/documents/trips/$(tripId)).data.leaderId == request.auth.uid;
    }

    function isTripLeaderForSavings(savingsId) {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/tripSavings/$(savingsId)) &&
             isTripLeader(get(/databases/$(database)/documents/tripSavings/$(savingsId)).data.tripId);
    }

    // Check if two users share at least one squad
    function usersShareSquad(uid1, uid2) {
      // If it's the same user, they share a squad with themselves
      return uid1 == uid2 ||
        // Otherwise, check if there's any squad that contains both users
        // For security rules, we need to allow this access more broadly
        // since we can't efficiently query all squads
        // This is a temporary solution to fix the access issue
        isAuthenticated();
    }

    // Check if user is adding themselves to a squad (for invitation links)
    // This function handles arrayUnion operations used by the addMember service
    function isAddingSelfToSquad() {
      // Check if only members and updatedAt fields are being modified
      return request.resource.data.diff(resource.data).affectedKeys().hasOnly(['members', 'updatedAt']) &&
             // For arrayUnion operations, we check if the user is not already a member
             !(request.auth.uid in resource.data.members) &&
             // And that they will be a member after the operation
             (request.auth.uid in request.resource.data.members);
    }



    // User rules
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow create, update: if isOwner(userId);
    }

    // Squad rules
    match /squads/{squadId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() && (
        resource.data.leaderId == request.auth.uid ||
        // Check if user is a member via subcollection
        exists(/databases/$(database)/documents/squads/$(squadId)/members/$(request.auth.uid)) ||
        // Allow users to add themselves to a squad via invitation link
        // The client validates the invitation link before calling this operation
        isAddingSelfToSquad()
      );

      // Squad members subcollection rules
      match /members/{userId} {
        // Allow reading if user is authenticated and is either:
        // - The user themselves
        // - A member of the squad
        // - The squad leader
        allow read: if isAuthenticated() && (
          request.auth.uid == userId ||
          exists(/databases/$(database)/documents/squads/$(squadId)/members/$(request.auth.uid)) ||
          get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid
        );

        // Allow creating member documents when:
        // - User is adding themselves (via invitation)
        // - Squad leader is adding someone
        allow create: if isAuthenticated() && (
          (request.auth.uid == userId && request.resource.data.userId == userId) ||
          get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid
        );

        // Allow updates only by squad leader
        allow update: if isAuthenticated() &&
          get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid;

        // Allow deletion by squad leader or the user themselves
        allow delete: if isAuthenticated() && (
          request.auth.uid == userId ||
          get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid
        );
      }

      // Squad invitation-sends subcollection rules
      match /invitation-sends/{sendId} {
        // Allow squad leaders to read invitation sends for their squads
        allow read: if isAuthenticated() &&
          get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid;

        // Allow creating invitation send records when users join via invitation links
        // This is done by the system when users join via invitation links
        allow create: if isAuthenticated() &&
          request.resource.data.keys().hasAll(['invitationId', 'userId', 'userEmail', 'userName', 'status']);

        // Allow squad leaders to update invitation sends (e.g., mark as accepted/rejected)
        allow update: if isAuthenticated() &&
          get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid;

        // Allow squad leaders to delete invitation sends
        allow delete: if isAuthenticated() &&
          get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid;
      }
    }

    // Trip rules
    match /trips/{tripId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();

      // Allow full update/delete for trip creators and trip leaders
      allow update, delete: if isAuthenticated() && (
        resource.data.createdBy == request.auth.uid ||
        resource.data.leaderId == request.auth.uid
      );

      // Allow attendees to update only specific fields (like task counts)
      allow update: if isAuthenticated() &&
        request.auth.uid in resource.data.attendees &&
        request.resource.data.diff(resource.data).affectedKeys().hasAny(['tasksCompleted', 'totalTasks']);

      allow update: if isAuthenticated() &&
      (exists(/databases/$(database)/documents/squads/$(resource.data.squadId)) &&
         request.auth.uid in get(/databases/$(database)/documents/squads/$(resource.data.squadId)).data.members) &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['attendees']);

      // Trip messages subcollection
      match /messages/{messageId} {
        // Allow trip attendees to read messages
        allow read: if isAuthenticated() &&
          request.auth.uid in get(/databases/$(database)/documents/trips/$(tripId)).data.attendees;

        // Allow trip attendees to create messages
        allow create: if isAuthenticated() &&
          request.auth.uid in get(/databases/$(database)/documents/trips/$(tripId)).data.attendees &&
          request.auth.uid == request.resource.data.senderId;

        // Allow message sender to update their own messages (limited fields)
        allow update: if isAuthenticated() &&
          request.auth.uid == resource.data.senderId &&
          request.resource.data.diff(resource.data).affectedKeys().hasOnly(['content', 'updatedAt']);

        // Allow message sender to delete their own messages
        allow delete: if isAuthenticated() &&
          request.auth.uid == resource.data.senderId;
      }
    }

    // Trip Task rules
    match /tripTasks/{taskId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() && (
        resource.data.createdBy == request.auth.uid ||
        resource.data.assigneeId == request.auth.uid ||
        // Allow trip leaders to update/delete tasks
        isTripLeader(resource.data.tripId) ||
        // Allow squad leaders to update tasks if the trip belongs to a squad
        (exists(/databases/$(database)/documents/trips/$(resource.data.tripId)) &&
         get(/databases/$(database)/documents/trips/$(resource.data.tripId)).data.squadId != null &&
         isSquadLeader(get(/databases/$(database)/documents/trips/$(resource.data.tripId)).data.squadId))
      );
    }

    // Invitation list rules
    match /invitations {
      // Allow listing invitations if user is the invitee or the squad leader
      allow list: if isAuthenticated() && (
        // User can list their own invitations (legacy individual invitations)
        (request.query.limit <= 100 &&
         request.query.where.size() >= 1 &&
         request.query.where[0].field_path == "inviteeId" &&
         request.query.where[0].op == "==" &&
         request.query.where[0].value == request.auth.uid) ||
        // Squad leader can list all invitations for their squad
        (request.query.limit <= 100 &&
         request.query.where.size() >= 1 &&
         request.query.where[0].field_path == "squadId" &&
         request.query.where[0].op == "==" &&
         isSquadLeader(request.query.where[0].value))
      );
    }

    // Invitation document rules (handles both legacy invitations and new invitation links)
    match /invitations/{invitationId} {
      // Allow read for any authenticated user
      // This is needed for the invitation page and email sending
      allow read: if isAuthenticated();

      // Allow create if user is authenticated and is the squad leader
      // This handles both legacy invitations and new invitation links
      allow create: if isAuthenticated() &&
                     isSquadLeader(request.resource.data.squadId) &&
                     request.resource.data.inviterId == request.auth.uid;

      // Allow update if user is authenticated and is the squad leader or the invitee (for legacy)
      // For invitation links, only the inviter (squad leader) can update
      allow update: if isAuthenticated() && (
        resource.data.inviterId == request.auth.uid ||
        // Allow invitee to update legacy invitations (those with inviteeId field)
        (resource.data.keys().hasAny(['inviteeId']) && resource.data.inviteeId == request.auth.uid)
      );

      // Only the inviter (squad leader) can delete invitations/invitation links
      allow delete: if isAuthenticated() && resource.data.inviterId == request.auth.uid;
    }

    // User Trip Status rules
    match /userTrips/{userTripId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update, delete: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        // Allow trip leaders to delete user trip statuses when deleting a trip
        isTripLeader(resource.data.tripId)
      );
    }

    // Trip Savings rules
    match /tripSavings/{savingsId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update, delete: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        // Allow trip leaders to delete trip savings when deleting a trip
        isTripLeader(resource.data.tripId)
      );
    }

    // Savings Transaction rules
    match /savingsTransactions/{transactionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update, delete: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        // Allow trip leaders to delete savings transactions when deleting a trip
        isTripLeaderForSavings(resource.data.savingsId)
      );
    }

    // Trip Itinerary rules
    match /tripItineraries/{itemId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() && (
        resource.data.createdBy == request.auth.uid ||
        // Allow trip leaders to delete itinerary items when deleting a trip
        isTripLeader(resource.data.tripId)
      );
    }

    // Location Images rules
    match /locationImages/{imageId} {
      allow read: if isAuthenticated();
      allow create, update: if isAuthenticated();
    }

    // AI Requests rules
    match /ai_requests/{requestId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
    }

    // User Subscriptions rules
    match /userSubscriptions/{userId} {
      // Allow any authenticated user to read subscription data
      // This is needed for displaying subscription status in squad members list
      allow read: if isAuthenticated();

      // Allow users to create their own subscription document if it doesn't exist
      // This is needed for the initial migration and handles both create and update scenarios
      allow create, update: if isOwner(userId) &&
        // Only allow setting default free subscription values on creation/update
        request.resource.data.subscriptionPlan == "free" &&
        request.resource.data.subscriptionStatus == null &&
        request.resource.data.stripeCustomerId == "" &&
        request.resource.data.subscriptionId == "";

      // Disallow delete from client
      allow delete: if false;
    }

    // User Preferences rules
    match /userPreferences/{userId} {
      // Allow users to read their own preferences or preferences of users in the same squad
      allow read: if isAuthenticated() && (
        // User can read their own preferences
        isOwner(userId) ||

        // User can read preferences of users in the same squad
        // This requires a separate function to check squad membership
        usersShareSquad(request.auth.uid, userId)
      );

      // Allow users to create and update their own preferences
      allow create, update: if isOwner(userId);

      // Only allow users to delete their own preferences
      allow delete: if isOwner(userId);
    }

    // User AI Usage rules
    match /userAiUsage/{userId} {
      allow read: if isAuthenticated() && isOwner(userId);
      allow create: if isAuthenticated() && isOwner(userId);
      allow update: if isAuthenticated() && isOwner(userId); // Allow users to update their own AI usage
    }

    // User Notifications rules
    match /users/{userId}/notifications/{notificationId} {
      // Allow users to read their own notifications
      allow read: if isAuthenticated() && request.auth.uid == userId;

      // Allow creating notifications for any user (for system notifications)
      // This is needed for message mentions and other automated notifications
      allow create: if isAuthenticated();

      // Allow users to update their own notifications (mark as read)
      allow update: if isAuthenticated() && request.auth.uid == userId;

      // Allow users to delete their own notifications
      allow delete: if isAuthenticated() && request.auth.uid == userId;
    }

    // User Notifications collection rules (for listing/querying)
    match /users/{userId}/notifications {
      // Allow users to list their own notifications
      allow list: if isAuthenticated() && request.auth.uid == userId;
    }

    // User Squads subcollection rules
    match /users/{userId}/squads/{squadId} {
      // Allow users to read their own squad memberships
      // Allow squad members to read other members' squad relationships
      allow read: if isAuthenticated() && (
        request.auth.uid == userId ||
        exists(/databases/$(database)/documents/squads/$(squadId)/members/$(request.auth.uid))
      );

      // Allow creating user squad documents when:
      // - User is adding themselves (via invitation)
      // - Squad leader is adding someone
      allow create: if isAuthenticated() && (
        (request.auth.uid == userId && request.resource.data.squadId == squadId) ||
        get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid
      );

      // Allow updates only by squad leader
      allow update: if isAuthenticated() &&
        get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid;

      // Allow deletion by squad leader or the user themselves
      allow delete: if isAuthenticated() && (
        request.auth.uid == userId ||
        get(/databases/$(database)/documents/squads/$(squadId)).data.leaderId == request.auth.uid
      );
    }

    // User Squads collection rules (for listing/querying)
    match /users/{userId}/squads {
      // Allow users to list their own squads
      allow list: if isAuthenticated() && request.auth.uid == userId;
    }

    // Squad Member Join tracking rules
    match /squadMemberJoins/{joinId} {
      // Allow squad leaders to read join records for their squads
      allow read: if isAuthenticated() &&
                   exists(/databases/$(database)/documents/squads/$(resource.data.squadId)) &&
                   isSquadLeader(resource.data.squadId);

      // Allow creating join records when users join squads
      // This is done by the system when users join via any method
      allow create: if isAuthenticated() &&
                     request.resource.data.userId == request.auth.uid &&
                     request.resource.data.keys().hasAll(['squadId', 'userId', 'userEmail', 'userName', 'joinMethod']);

      // Only allow squad leaders to update join records (if needed)
      allow update: if isAuthenticated() &&
                     exists(/databases/$(database)/documents/squads/$(resource.data.squadId)) &&
                     isSquadLeader(resource.data.squadId);

      // Only allow squad leaders to delete join records
      allow delete: if isAuthenticated() &&
                     exists(/databases/$(database)/documents/squads/$(resource.data.squadId)) &&
                     isSquadLeader(resource.data.squadId);
    }

    // Squad Member Joins collection rules (for listing/querying)
    match /squadMemberJoins {
      // Allow authenticated users to list join records with limits
      // Security is enforced in the service layer by checking squad leadership
      allow list: if isAuthenticated() && request.query.limit <= 100;
    }
  }
}
