import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Squad entity
 */
export interface Squad extends BaseEntity {
  name: string
  description?: string
  leaderId: string
  memberCount?: number // Total number of members
}

/**
 * Squad member subcollection document
 */
export interface SquadMember extends BaseEntity {
  userId: string
  joinedAt: Timestamp
  role: "leader" | "member"
  invitedBy?: string
  joinMethod: "email_invitation" | "shareable_link" | "legacy_invitation"
}

/**
 * User squad subcollection document
 */
export interface UserSquad extends BaseEntity {
  squadId: string
  squadName: string
  role: "leader" | "member"
  joinedAt: Timestamp
  lastActivity?: Timestamp
}

/**
 * Squad member join tracking
 */
export interface SquadMemberJoin extends BaseEntity {
  squadId: string
  userId: string
  userEmail: string
  userName: string
  joinMethod: "email_invitation" | "shareable_link" | "legacy_invitation"
  invitationId?: string // Reference to invitation link or legacy invitation
  joinedAt: Timestamp
  invitedBy?: string // User ID of who sent the invitation (for email invitations)
}

/**
 * Squad creation data
 */
export type SquadCreateData = Omit<Squad, "id" | "createdAt" | "memberCount">

/**
 * Squad member creation data
 */
export type SquadMemberCreateData = Omit<SquadMember, "id" | "createdAt" | "updatedAt">

/**
 * User squad creation data
 */
export type UserSquadCreateData = Omit<UserSquad, "id" | "createdAt" | "updatedAt">

/**
 * Squad update data
 */
export type SquadUpdateData = Partial<Squad>
