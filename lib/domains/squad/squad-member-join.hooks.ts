import { useState, useEffect } from "react"
import { SquadMemberJoinService } from "./squad-member-join.service"
import { SquadMemberJoin } from "./squad.types"
import { useUser } from "../auth/auth.hooks"

/**
 * Hook to get squad join history
 */
export const useSquadJoinHistory = (squadId: string, limitCount: number = 50) => {
  const user = useUser()
  const [joinHistory, setJoinHistory] = useState<SquadMemberJoin[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!squadId || !user?.uid) {
      setJoinHistory([])
      setLoading(false)
      return
    }

    const fetchJoinHistory = async () => {
      try {
        setLoading(true)
        setError(null)

        const result = await SquadMemberJoinService.getSquadJoinHistory(
          squadId,
          user.uid,
          limitCount
        )

        if (result.success) {
          setJoinHistory(result.data || [])
        } else {
          setError(result.error as Error)
        }
      } catch (err) {
        console.error("Error fetching squad join history:", err)
        setError(err as Error)
      } finally {
        setLoading(false)
      }
    }

    fetchJoinHistory()
  }, [squadId, user?.uid, limitCount])

  return { joinHistory, loading, error }
}

/**
 * Hook to get squad join statistics
 */
export const useSquadJoinStats = (squadId: string) => {
  const user = useUser()
  const [stats, setStats] = useState<{
    totalJoins: number
    emailInvitations: number
    shareableLinkJoins: number
    legacyInvitations: number
  } | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!squadId || !user?.uid) {
      setStats(null)
      setLoading(false)
      return
    }

    const fetchStats = async () => {
      try {
        setLoading(true)
        setError(null)

        const result = await SquadMemberJoinService.getSquadJoinStats(squadId, user.uid)

        if (result.success) {
          setStats(result.data || null)
        } else {
          setError(result.error as Error)
        }
      } catch (err) {
        console.error("Error fetching squad join stats:", err)
        setError(err as Error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [squadId, user?.uid])

  return { stats, loading, error }
}

/**
 * Hook to get join record for a specific user
 */
export const useUserJoinRecord = (squadId: string, userId: string) => {
  const [joinRecord, setJoinRecord] = useState<SquadMemberJoin | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!squadId || !userId) {
      setJoinRecord(null)
      setLoading(false)
      return
    }

    const fetchJoinRecord = async () => {
      try {
        setLoading(true)
        setError(null)

        const result = await SquadMemberJoinService.getUserJoinRecord(squadId, userId)

        if (result.success) {
          setJoinRecord(result.data || null)
        } else {
          setError(result.error as Error)
        }
      } catch (err) {
        console.error("Error fetching user join record:", err)
        setError(err as Error)
      } finally {
        setLoading(false)
      }
    }

    fetchJoinRecord()
  }, [squadId, userId])

  return { joinRecord, loading, error }
}
